import Session from '../../common/Session';

Page({
  data: {
    
  },

  onLoad() {
    // 检查用户是否已登录
    const userInfo = Session.getUser();
    if (userInfo && userInfo.id) {
      // 已登录，直接跳转到首页
      wx.switchTab({
        url: '/pages/index/index',
      });
    }
  },

  // 跳转到登录页面
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/index',
    });
  },

  onShow() {
    // 每次显示页面时检查登录状态
    const userInfo = Session.getUser();
    if (userInfo && userInfo.id) {
      // 已登录，直接跳转到首页
      wx.switchTab({
        url: '/pages/index/index',
      });
    }
  }
});
