/* pages/guide/index.wxss */
.guide-page {
  background-color: rgba(246, 246, 246, 1);
  min-height: 100vh;
  position: relative;
}

.guide-wrapper {
  padding: 40rpx 40rpx 0rpx;
  background: rgba(47, 131, 255, 0.1);
  overflow: hidden;
  border-radius: 0 0 50vw 50vw;
}

.logo-text {
  text-align: center;
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  line-height: 70rpx;
  color: #333;
}

.logo-image-wrapper,
.guide-image-wrapper {
  text-align: center;
}

.logo-image-wrapper image {
  width: 50vw;
}

.guide-image-wrapper image {
  width: 60vw;
  margin-top: 60rpx;
}

.content-container {
  margin: 60rpx 40rpx 0;
}

.notice-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 60rpx;
}

.notice-icon {
  margin-right: 20rpx;
  margin-top: 5rpx;
}

.icon-info {
  font-size: 40rpx;
}

.notice-content {
  flex: 1;
}

.notice-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.notice-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.guide-steps {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.step-item {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background: rgba(47, 131, 255, 1);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.step-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.btn-container {
  margin: 80rpx auto 60rpx;
  width: 80vw;
}

.login-btn {
  background: rgba(47, 131, 255, 1);
  box-shadow: 0px 3px 0px 0px rgba(47, 131, 255, 0.3);
  color: #fff;
  border-radius: 40rpx;
  margin-bottom: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  height: 88rpx;
  line-height: 88rpx;
}

.tip-text {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}
